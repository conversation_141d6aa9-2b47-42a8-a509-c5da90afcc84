from uuid import uuid4

from sqlalchemy import Column, UUID, String, DateTime, ForeignKey, JSON, Text, UniqueConstraint, func
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class User(Base):
    __tablename__ = 'users'
    id = Column(UUID, primary_key=True, default=uuid4)
    firebase_user_id = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    created_at = Column(DateTime, server_default=func.now())


class JournalEntry(Base):
    __tablename__ = 'journal_entries'
    title = Column(String, nullable=True)
    content = Column(Text, nullable=False)
    local_timestamp = Column(DateTime, nullable=False)

    week = Column(String, nullable=False)
    month = Column(String, nullable=False)

    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, server_default=func.now())


class Agent(Base):
    __tablename__ = 'agents'
    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey('users.id'), nullable=False)
    name = Column(String(50), nullable=False)
    prompt = Column(Text, nullable=True)  # Agent's system prompt
    tools = Column(JSON, nullable=True)  # For future use
    created_at = Column(DateTime, server_default=func.now())


class AgentSubscription(Base):
    __tablename__ = 'agent_subscriptions'
    id = Column(UUID, primary_key=True, default=uuid4)
    agent_id = Column(UUID, ForeignKey('agents.id'), nullable=False)
    channel = Column(String(50), nullable=False)  # Channel name
    created_at = Column(DateTime, server_default=func.now())

    # Composite unique constraint - one subscription per ai per channel
    __table_args__ = (UniqueConstraint('agent_id', 'channel'),)


class Message(Base):
    __tablename__ = 'messages'
    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, server_default=func.now())

    sender = Column(String(50), nullable=False)
    payload = Column(JSON, nullable=False)


class AgentNote(Base):
    __tablename__ = 'agent_notes'
    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey('users.id'), nullable=False)
    agent_name = Column(String(50), nullable=False)  # 'eforos', 'safine'
    notes = Column(Text, nullable=False)  # Big text blob
    updated_at = Column(DateTime, onupdate=func.now())

    # Composite unique constraint - one note per ai per user
    __table_args__ = (UniqueConstraint('user_id', 'agent_name'),)


class Slate(Base):
    __tablename__ = 'slate_versions'
    id = Column(UUID, primary_key=True, default=uuid4)
    user_id = Column(UUID, ForeignKey('users.id'), nullable=False)
    content = Column(JSON, nullable=False)  # Structured slate data
    updated_at = Column(DateTime, onupdate=func.now())
