from datetime import datetime
from typing import List, Dict, Any, Annotated

from fastapi import APIRouter, Depends, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.orm import Session
from starlette import status

from ai.agent import ai
from db.models import Agent, AgentSubscription, User
from db.session import get_db_session

router = APIRouter(
    prefix="/ai",
    tags=["ai"],
)

class MessageNotification(BaseModel):
    channel: str
    message: str
    sender: str
    user_id: str


class AgentResponse(BaseModel):
    agent_name: str
    response: str
    actions: List[Dict[str, Any]] = []


async def process_agent_message(
        message_notification: MessageNotification,
        user: User,
        agent: Agent):
    """
    Process a message for a specific ai.
    This function creates a prompt for the ai and augments it with message information.
    TODO: Consider moving this to its own file.
    """
    # Todo change datetime to use user's timezone.
    user_info = f"""
    Your user is {user.email}. The current time is {datetime.now()}.
    Currently, we're just testing the code to give you tool calling functionality.
    Try to just do everything that you think you need to do. Then, just log it all into a note, so that we can inspect
    how you're doing.
    """
    message_info = f"""
    An ai or process has sent a message: {message_notification.message}.
    The sender is {message_notification.sender}.
    """
    agent_prompt = agent.prompt.format(
        # Place any information in here that can be used to augment the ai's understanding of the situation.
    )

    augmented_prompt = f"""
    {agent_prompt}
    {user_info}
    {message_info}
    """
    print(f"Processing prompt for {agent.name}: {augmented_prompt}")

    result = await ai.generate(
        prompt=augmented_prompt,
        tools=[agent.tools],
    )

    print(f"Agent response: {result}")


@router.post("/message", status_code=status.HTTP_202_ACCEPTED)
async def new_message(
        message_notification: MessageNotification,
        background_tasks: BackgroundTasks,
        db: Annotated[Session, Depends(get_db_session)]):
    """
    Handle a new message notification.
    This endpoint fetches agents subscribed to the specified channel and processes the message.
    """
    # Fetch agents that are subscribed to this channel
    agent_query = select(Agent).join(AgentSubscription).filter(AgentSubscription.channel == message_notification.channel)
    agents = db.execute(agent_query).scalars().all()
    user = db.execute(select(User).filter(User.id == message_notification.user_id)).scalar_one()

    if not agents:
        print("No agents found.")
        return []

    # Process the message for each ai
    for agent in agents:
        # Add the task to background processing
        background_tasks.add_task(
            process_agent_message,
            message_notification,
            user,
            agent,
        )

    return {"message": f"Message accepted. Processing for {len(agents)} ai(s) has been initiated."}
