import httpx
import asyncio
from uuid import UUID

from db.models import Message
from db.session import get_db_session


async def notify_agent_endpoint(user_id: UUID,channel: str, message: str, sender: str):
    """
    Notify the agent endpoint about a new message.
    This function makes an HTTP request to the agent endpoint.
    """
    async with httpx.AsyncClient() as client:
        try:
            payload = {
                "user_id": user_id,
                "channel": channel,
                "message": message,
                "sender": sender
            }
            
            if user_id:
                payload["user_id"] = user_id
                
            # Make a request to the agent endpoint
            # In a real application, you'd use a proper URL and handle authentication
            response = await client.post(
                "http://localhost:8000/agent/message",
                json=payload
            )
            
            return response.json()
        except Exception as e:
            print(f"Error notifying agent endpoint: {e}")
            return None

def send_message(user_id: UUID, channel: str, message: str, sender: str):
    """
    Send a message to a channel.
    This function persists the message to the database and notifies the agent endpoint.
    """
    db = get_db_session()
    db_message = Message(
        user_id=user_id,
        sender=sender,
        payload={
            "channel": channel,
            "message": message
        }
    )
    db.add(db_message)
    db.commit()

    asyncio.create_task(notify_agent_endpoint(user_id, channel, message, sender))

    return db_message