from datetime import datetime
from uuid import UUID

from genkit.ai import Genkit
from pydantic import BaseModel

from agent.comms.channels import send_message
from db.models import AgentNote
from db.session import get_db_session

ai = Genkit(
    model='googleai/gemini-2.5-flash',
)

class SendMessageInput(BaseModel):
    user_id: UUID
    channel: str
    message: str
    sender: str

class ReadNotesInput(BaseModel):
    user_id: UUID
    agent_name: str

class WriteNotesInput(BaseModel):
    user_id: UUID
    agent_name: str
    notes: str

@ai.tool()
def send_message_tool(sendmessageInput:SendMessageInput):
    """
    Send a message to a channel.
    """
    return send_message(sendmessageInput.user_id, sendmessageInput.channel, sendmessageInput.message, sendmessageInput.sender)

@ai.tool()
def read_notes(read_notes_input: ReadNotesInput):
    db = get_db_session()
    note = db.query(AgentNote).filter(AgentNote.user_id == read_notes_input.user_id, AgentNote.agent_name == read_notes_input.agent_name).first()
    if note:
        return note.notes
    else:
        return ""

@ai.tool()
def write_notes(write_notes_input: WriteNotesInput):
    db = get_db_session()
    note = db.query(AgentNote).filter(AgentNote.user_id == write_notes_input.user_id, AgentNote.agent_name == write_notes_input.agent_name).first()
    if note:
        note.notes = write_notes_input.notes
    else:
        note = AgentNote(user_id=write_notes_input.user_id, agent_name=write_notes_input.agent_name, notes=write_notes_input.notes)
        db.add(note)
    db.commit()
    return "Notes saved"

@ai.tool()
def get_current_time():
    # TODO: Think about getting latest user timezone.
    return datetime.now()

@ai.tool()
def get_hourly_weather():
    # TODO: Get user location and get weather for that location.
    # Can use weatherkit once I get on that Apple developer program.
    return "Sunny and 72 degrees. This is just example weather data by the way. Actual weather API integration will come in the future."
