"""Add user_id to ai

Revision ID: dfbeb90c24fd
Revises: 8b98f5226a8c
Create Date: 2025-06-24 17:01:05.471575

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dfbeb90c24fd'
down_revision: Union[str, Sequence[str], None] = '8b98f5226a8c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('user_id', sa.UUID(), nullable=False))
    op.create_foreign_key(None, 'agents', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'agents', type_='foreignkey')
    op.drop_column('agents', 'user_id')
    # ### end Alembic commands ###
